"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/utils/SearchBtn.tsx":
/*!****************************************!*\
  !*** ./components/utils/SearchBtn.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SearchBtn: () => (/* binding */ SearchBtn)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useNewApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useNewApi */ \"(app-pages-browser)/./hooks/useNewApi.ts\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _product_ProductInfiniteScrolling__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../product/ProductInfiniteScrolling */ \"(app-pages-browser)/./components/product/ProductInfiniteScrolling.tsx\");\n/* harmony import */ var _hooks_useInfiniteScroll__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useInfiniteScroll */ \"(app-pages-browser)/./hooks/useInfiniteScroll.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst SearchBtn = ()=>{\n    _s();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [currentQuery, setCurrentQuery] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const { page, loading, setLoading, setHasMore } = (0,_hooks_useInfiniteScroll__WEBPACK_IMPORTED_MODULE_5__.useInfiniteScroll)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const { get } = (0,_hooks_useNewApi__WEBPACK_IMPORTED_MODULE_2__.useNewApi)({\n        baseURL: _constant_urls__WEBPACK_IMPORTED_MODULE_1__.MAIN_URL\n    });\n    const observerTarget = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const handleProductSearch = async function() {\n        let isNewSearch = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setLoading(true);\n        let url = \"\".concat(_constant_urls__WEBPACK_IMPORTED_MODULE_1__.PRODUCTS, \"?search=\").concat(currentQuery, \"&page=\").concat(page);\n        const response = await get(url);\n        if (response.data) {\n            var _data_results;\n            const data = response.data;\n            if (data === null || data === void 0 ? void 0 : data.next) {\n                setHasMore(true);\n            } else {\n                setHasMore(false);\n            }\n            if (Boolean((data === null || data === void 0 ? void 0 : (_data_results = data.results) === null || _data_results === void 0 ? void 0 : _data_results.length) > 0)) {\n                if (isNewSearch) {\n                    // Replace products for new searches\n                    setProducts(data === null || data === void 0 ? void 0 : data.results);\n                } else {\n                    // Append products for pagination\n                    setProducts((prev)=>{\n                        // Create a Set of existing product IDs to avoid duplicates\n                        const existingIds = new Set(prev.map((product)=>product.id));\n                        // Filter out any products that already exist in the array\n                        const newProducts = data === null || data === void 0 ? void 0 : data.results.filter((product)=>!existingIds.has(product.id));\n                        return [\n                            ...prev,\n                            ...newProducts\n                        ];\n                    });\n                }\n            } else if (isNewSearch) {\n                // Clear products if no results for a new search\n                setProducts([]);\n            }\n            setLoading(false);\n        }\n        if (response.error) {\n            // Handle error\n            setLoading(false);\n        }\n    };\n    // Effect for handling search query changes\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"SearchBtn.useEffect\": ()=>{\n            // If query is empty, clear results and exit\n            if (!searchQuery) {\n                setProducts([]);\n                setCurrentQuery(\"\");\n                return;\n            }\n            // Set a delay for the debounce\n            const handler = setTimeout({\n                \"SearchBtn.useEffect.handler\": ()=>{\n                    if (searchQuery !== currentQuery) {\n                        setCurrentQuery(searchQuery);\n                    }\n                }\n            }[\"SearchBtn.useEffect.handler\"], 500);\n            // Clean up the timeout if the query changes before delay completes\n            return ({\n                \"SearchBtn.useEffect\": ()=>clearTimeout(handler)\n            })[\"SearchBtn.useEffect\"];\n        }\n    }[\"SearchBtn.useEffect\"], [\n        searchQuery\n    ]);\n    // Effect for handling actual API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"SearchBtn.useEffect\": ()=>{\n            if (!currentQuery) return;\n            // If the query changed, reset page to 1 and treat as new search\n            const isNewSearch = page === 1;\n            handleProductSearch(isNewSearch);\n        }\n    }[\"SearchBtn.useEffect\"], [\n        currentQuery,\n        page\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-lg p-6 h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"w-[80vw] mx-auto flex items-center gap-2 bg-white border border-neutral-300 rounded-full px-4 py-2 hover:shadow-lg\",\n                onSubmit: (e)=>{\n                    e.preventDefault();\n                    if (searchQuery.trim()) {\n                        setCurrentQuery(searchQuery.trim());\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        placeholder: \"Search for products...\",\n                        value: searchQuery,\n                        onChange: (e)=>setSearchQuery(e.target.value),\n                        className: \"flex-1 px-4 py-2 bg-transparent focus:outline-none text-lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\utils\\\\SearchBtn.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"flex items-center justify-center h-[40px] w-[40px] bg-theme-accent-primary text-white rounded-full shadow-md hover:bg-theme-accent-hover transition-colors duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-5 w-5\",\n                            strokeWidth: 2.5\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\utils\\\\SearchBtn.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\utils\\\\SearchBtn.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\utils\\\\SearchBtn.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: searchQuery.trim() !== \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_ProductInfiniteScrolling__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    products: products,\n                    isLoading: loading,\n                    observerTarget: observerTarget\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\utils\\\\SearchBtn.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\utils\\\\SearchBtn.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\utils\\\\SearchBtn.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SearchBtn, \"zRRAMfPTq3CidL9l1a93cSNc69o=\", false, function() {\n    return [\n        _hooks_useInfiniteScroll__WEBPACK_IMPORTED_MODULE_5__.useInfiniteScroll,\n        _hooks_useNewApi__WEBPACK_IMPORTED_MODULE_2__.useNewApi\n    ];\n});\n_c = SearchBtn;\nvar _c;\n$RefreshReg$(_c, \"SearchBtn\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdXRpbHMvU2VhcmNoQnRuLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUFxRDtBQUNQO0FBQ1I7QUFDcUI7QUFDZ0I7QUFDYjtBQUV2RCxNQUFNVSxZQUFZOztJQUN2QixNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR0wsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDTSxjQUFjQyxnQkFBZ0IsR0FBR1AsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxFQUFFUSxJQUFJLEVBQUVDLE9BQU8sRUFBRUMsVUFBVSxFQUFFQyxVQUFVLEVBQUUsR0FDN0NULDJFQUFpQkE7SUFFbkIsTUFBTSxDQUFDVSxVQUFVQyxZQUFZLEdBQUdiLCtDQUFRQSxDQUFRLEVBQUU7SUFDbEQsTUFBTSxFQUFFYyxHQUFHLEVBQUUsR0FBR25CLDJEQUFTQSxDQUFDO1FBQ3hCb0IsU0FBU3RCLG9EQUFRQTtJQUNuQjtJQUNBLE1BQU11QixpQkFBaUJqQiw2Q0FBTUEsQ0FBaUI7SUFFOUMsTUFBTWtCLHNCQUFzQjtZQUFPQywrRUFBYztRQUMvQ1IsV0FBVztRQUNYLElBQUlTLE1BQU0sR0FBc0JiLE9BQW5CWixvREFBUUEsRUFBQyxZQUErQmMsT0FBckJGLGNBQWEsVUFBYSxPQUFMRTtRQUNyRCxNQUFNWSxXQUFnQixNQUFNTixJQUFJSztRQUNoQyxJQUFJQyxTQUFTQyxJQUFJLEVBQUU7Z0JBT0xBO1lBTlosTUFBTUEsT0FBT0QsU0FBU0MsSUFBSTtZQUMxQixJQUFJQSxpQkFBQUEsMkJBQUFBLEtBQU1DLElBQUksRUFBRTtnQkFDZFgsV0FBVztZQUNiLE9BQU87Z0JBQ0xBLFdBQVc7WUFDYjtZQUNBLElBQUlZLFFBQVFGLENBQUFBLGlCQUFBQSw0QkFBQUEsZ0JBQUFBLEtBQU1HLE9BQU8sY0FBYkgsb0NBQUFBLGNBQWVJLE1BQU0sSUFBRyxJQUFJO2dCQUN0QyxJQUFJUCxhQUFhO29CQUNmLG9DQUFvQztvQkFDcENMLFlBQVlRLGlCQUFBQSwyQkFBQUEsS0FBTUcsT0FBTztnQkFDM0IsT0FBTztvQkFDTCxpQ0FBaUM7b0JBQ2pDWCxZQUFZLENBQUNhO3dCQUNYLDJEQUEyRDt3QkFDM0QsTUFBTUMsY0FBYyxJQUFJQyxJQUFJRixLQUFLRyxHQUFHLENBQUMsQ0FBQ0MsVUFBaUJBLFFBQVFDLEVBQUU7d0JBQ2pFLDBEQUEwRDt3QkFDMUQsTUFBTUMsY0FBY1gsaUJBQUFBLDJCQUFBQSxLQUFNRyxPQUFPLENBQUNTLE1BQU0sQ0FBQyxDQUFDSCxVQUFpQixDQUFDSCxZQUFZTyxHQUFHLENBQUNKLFFBQVFDLEVBQUU7d0JBQ3RGLE9BQU87K0JBQUlMOytCQUFTTTt5QkFBWTtvQkFDbEM7Z0JBQ0Y7WUFDRixPQUFPLElBQUlkLGFBQWE7Z0JBQ3RCLGdEQUFnRDtnQkFDaERMLFlBQVksRUFBRTtZQUNoQjtZQUVBSCxXQUFXO1FBQ2I7UUFDQSxJQUFJVSxTQUFTZSxLQUFLLEVBQUU7WUFDbEIsZUFBZTtZQUNmekIsV0FBVztRQUNiO0lBQ0Y7SUFFQSwyQ0FBMkM7SUFDM0NaLGdEQUFTQTsrQkFBQztZQUNSLDRDQUE0QztZQUM1QyxJQUFJLENBQUNNLGFBQWE7Z0JBQ2hCUyxZQUFZLEVBQUU7Z0JBQ2ROLGdCQUFnQjtnQkFDaEI7WUFDRjtZQUVBLCtCQUErQjtZQUMvQixNQUFNNkIsVUFBVUM7K0NBQVc7b0JBQ3pCLElBQUlqQyxnQkFBZ0JFLGNBQWM7d0JBQ2hDQyxnQkFBZ0JIO29CQUNsQjtnQkFDRjs4Q0FBRztZQUVILG1FQUFtRTtZQUNuRTt1Q0FBTyxJQUFNa0MsYUFBYUY7O1FBQzVCOzhCQUFHO1FBQUNoQztLQUFZO0lBRWhCLHVDQUF1QztJQUN2Q04sZ0RBQVNBOytCQUFDO1lBQ1IsSUFBSSxDQUFDUSxjQUFjO1lBRW5CLGdFQUFnRTtZQUNoRSxNQUFNWSxjQUFjVixTQUFTO1lBQzdCUyxvQkFBb0JDO1FBQ3RCOzhCQUFHO1FBQUNaO1FBQWNFO0tBQUs7SUFFdkIscUJBQ0UsOERBQUMrQjtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQ0NELFdBQVU7Z0JBQ1ZFLFVBQVUsQ0FBQ0M7b0JBQ1RBLEVBQUVDLGNBQWM7b0JBQ2hCLElBQUl4QyxZQUFZeUMsSUFBSSxJQUFJO3dCQUN0QnRDLGdCQUFnQkgsWUFBWXlDLElBQUk7b0JBQ2xDO2dCQUNGOztrQ0FFQSw4REFBQ0M7d0JBQ0NDLE1BQUs7d0JBQ0xDLGFBQVk7d0JBQ1pDLE9BQU83Qzt3QkFDUDhDLFVBQVUsQ0FBQ1AsSUFBTXRDLGVBQWVzQyxFQUFFUSxNQUFNLENBQUNGLEtBQUs7d0JBQzlDVCxXQUFVOzs7Ozs7a0NBRVosOERBQUNZO3dCQUNDTCxNQUFLO3dCQUNMUCxXQUFVO2tDQUVWLDRFQUFDNUMsa0ZBQU1BOzRCQUFDNEMsV0FBVTs0QkFBVWEsYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSTdDLDhEQUFDZDtnQkFBSUMsV0FBVTswQkFDWnBDLFlBQVl5QyxJQUFJLE9BQU8sb0JBQ3RCLDhEQUFDNUMseUVBQXdCQTtvQkFDdkJXLFVBQVVBO29CQUNWMEMsV0FBVzdDO29CQUNYTyxnQkFBZ0JBOzs7Ozs7Ozs7Ozs7Ozs7OztBQU01QixFQUFFO0dBcEhXYjs7UUFJVEQsdUVBQWlCQTtRQUdIUCx1REFBU0E7OztLQVBkUSIsInNvdXJjZXMiOlsiRDpcXFRyaXVtcGhcXGVjb21tZXJjZVxcY29tcG9uZW50c1xcdXRpbHNcXFNlYXJjaEJ0bi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTUFJTl9VUkwsIFBST0RVQ1RTIH0gZnJvbSBcIkAvY29uc3RhbnQvdXJsc1wiO1xyXG5pbXBvcnQgeyB1c2VOZXdBcGkgfSBmcm9tIFwiQC9ob29rcy91c2VOZXdBcGlcIjtcclxuaW1wb3J0IHsgU2VhcmNoIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xyXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBQcm9kdWN0SW5maW5pdGVTY3JvbGxpbmcgZnJvbSBcIi4uL3Byb2R1Y3QvUHJvZHVjdEluZmluaXRlU2Nyb2xsaW5nXCI7XHJcbmltcG9ydCB7IHVzZUluZmluaXRlU2Nyb2xsIH0gZnJvbSBcIkAvaG9va3MvdXNlSW5maW5pdGVTY3JvbGxcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBTZWFyY2hCdG4gPSAoKSA9PiB7XHJcbiAgY29uc3QgW3NlYXJjaFF1ZXJ5LCBzZXRTZWFyY2hRdWVyeV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbY3VycmVudFF1ZXJ5LCBzZXRDdXJyZW50UXVlcnldID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgeyBwYWdlLCBsb2FkaW5nLCBzZXRMb2FkaW5nLCBzZXRIYXNNb3JlIH0gPVxyXG4gICAgdXNlSW5maW5pdGVTY3JvbGwoKTtcclxuXHJcbiAgY29uc3QgW3Byb2R1Y3RzLCBzZXRQcm9kdWN0c10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xyXG4gIGNvbnN0IHsgZ2V0IH0gPSB1c2VOZXdBcGkoe1xyXG4gICAgYmFzZVVSTDogTUFJTl9VUkwsXHJcbiAgfSk7XHJcbiAgY29uc3Qgb2JzZXJ2ZXJUYXJnZXQgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xyXG5cclxuICBjb25zdCBoYW5kbGVQcm9kdWN0U2VhcmNoID0gYXN5bmMgKGlzTmV3U2VhcmNoID0gZmFsc2UpID0+IHtcclxuICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcbiAgICBsZXQgdXJsID0gYCR7UFJPRFVDVFN9P3NlYXJjaD0ke2N1cnJlbnRRdWVyeX0mcGFnZT0ke3BhZ2V9YDtcclxuICAgIGNvbnN0IHJlc3BvbnNlOiBhbnkgPSBhd2FpdCBnZXQodXJsKTtcclxuICAgIGlmIChyZXNwb25zZS5kYXRhKSB7XHJcbiAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5kYXRhO1xyXG4gICAgICBpZiAoZGF0YT8ubmV4dCkge1xyXG4gICAgICAgIHNldEhhc01vcmUodHJ1ZSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0SGFzTW9yZShmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgICAgaWYgKEJvb2xlYW4oZGF0YT8ucmVzdWx0cz8ubGVuZ3RoID4gMCkpIHtcclxuICAgICAgICBpZiAoaXNOZXdTZWFyY2gpIHtcclxuICAgICAgICAgIC8vIFJlcGxhY2UgcHJvZHVjdHMgZm9yIG5ldyBzZWFyY2hlc1xyXG4gICAgICAgICAgc2V0UHJvZHVjdHMoZGF0YT8ucmVzdWx0cyk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIC8vIEFwcGVuZCBwcm9kdWN0cyBmb3IgcGFnaW5hdGlvblxyXG4gICAgICAgICAgc2V0UHJvZHVjdHMoKHByZXYpID0+IHtcclxuICAgICAgICAgICAgLy8gQ3JlYXRlIGEgU2V0IG9mIGV4aXN0aW5nIHByb2R1Y3QgSURzIHRvIGF2b2lkIGR1cGxpY2F0ZXNcclxuICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdJZHMgPSBuZXcgU2V0KHByZXYubWFwKChwcm9kdWN0OiBhbnkpID0+IHByb2R1Y3QuaWQpKTtcclxuICAgICAgICAgICAgLy8gRmlsdGVyIG91dCBhbnkgcHJvZHVjdHMgdGhhdCBhbHJlYWR5IGV4aXN0IGluIHRoZSBhcnJheVxyXG4gICAgICAgICAgICBjb25zdCBuZXdQcm9kdWN0cyA9IGRhdGE/LnJlc3VsdHMuZmlsdGVyKChwcm9kdWN0OiBhbnkpID0+ICFleGlzdGluZ0lkcy5oYXMocHJvZHVjdC5pZCkpO1xyXG4gICAgICAgICAgICByZXR1cm4gWy4uLnByZXYsIC4uLm5ld1Byb2R1Y3RzXTtcclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBlbHNlIGlmIChpc05ld1NlYXJjaCkge1xyXG4gICAgICAgIC8vIENsZWFyIHByb2R1Y3RzIGlmIG5vIHJlc3VsdHMgZm9yIGEgbmV3IHNlYXJjaFxyXG4gICAgICAgIHNldFByb2R1Y3RzKFtdKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgICBpZiAocmVzcG9uc2UuZXJyb3IpIHtcclxuICAgICAgLy8gSGFuZGxlIGVycm9yXHJcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIEVmZmVjdCBmb3IgaGFuZGxpbmcgc2VhcmNoIHF1ZXJ5IGNoYW5nZXNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gSWYgcXVlcnkgaXMgZW1wdHksIGNsZWFyIHJlc3VsdHMgYW5kIGV4aXRcclxuICAgIGlmICghc2VhcmNoUXVlcnkpIHtcclxuICAgICAgc2V0UHJvZHVjdHMoW10pO1xyXG4gICAgICBzZXRDdXJyZW50UXVlcnkoXCJcIik7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyBTZXQgYSBkZWxheSBmb3IgdGhlIGRlYm91bmNlXHJcbiAgICBjb25zdCBoYW5kbGVyID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgIGlmIChzZWFyY2hRdWVyeSAhPT0gY3VycmVudFF1ZXJ5KSB7XHJcbiAgICAgICAgc2V0Q3VycmVudFF1ZXJ5KHNlYXJjaFF1ZXJ5KTtcclxuICAgICAgfVxyXG4gICAgfSwgNTAwKTtcclxuXHJcbiAgICAvLyBDbGVhbiB1cCB0aGUgdGltZW91dCBpZiB0aGUgcXVlcnkgY2hhbmdlcyBiZWZvcmUgZGVsYXkgY29tcGxldGVzXHJcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KGhhbmRsZXIpO1xyXG4gIH0sIFtzZWFyY2hRdWVyeV0pO1xyXG5cclxuICAvLyBFZmZlY3QgZm9yIGhhbmRsaW5nIGFjdHVhbCBBUEkgY2FsbHNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCFjdXJyZW50UXVlcnkpIHJldHVybjtcclxuXHJcbiAgICAvLyBJZiB0aGUgcXVlcnkgY2hhbmdlZCwgcmVzZXQgcGFnZSB0byAxIGFuZCB0cmVhdCBhcyBuZXcgc2VhcmNoXHJcbiAgICBjb25zdCBpc05ld1NlYXJjaCA9IHBhZ2UgPT09IDE7XHJcbiAgICBoYW5kbGVQcm9kdWN0U2VhcmNoKGlzTmV3U2VhcmNoKTtcclxuICB9LCBbY3VycmVudFF1ZXJ5LCBwYWdlXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInJvdW5kZWQtbGcgcC02IGgtZnVsbFwiPlxyXG4gICAgICA8Zm9ybVxyXG4gICAgICAgIGNsYXNzTmFtZT1cInctWzgwdnddIG14LWF1dG8gZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTMwMCByb3VuZGVkLWZ1bGwgcHgtNCBweS0yIGhvdmVyOnNoYWRvdy1sZ1wiXHJcbiAgICAgICAgb25TdWJtaXQ9eyhlKSA9PiB7XHJcbiAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XHJcbiAgICAgICAgICBpZiAoc2VhcmNoUXVlcnkudHJpbSgpKSB7XHJcbiAgICAgICAgICAgIHNldEN1cnJlbnRRdWVyeShzZWFyY2hRdWVyeS50cmltKCkpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH19XHJcbiAgICAgID5cclxuICAgICAgICA8aW5wdXRcclxuICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIGZvciBwcm9kdWN0cy4uLlwiXHJcbiAgICAgICAgICB2YWx1ZT17c2VhcmNoUXVlcnl9XHJcbiAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFF1ZXJ5KGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBweC00IHB5LTIgYmctdHJhbnNwYXJlbnQgZm9jdXM6b3V0bGluZS1ub25lIHRleHQtbGdcIlxyXG4gICAgICAgIC8+XHJcbiAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLVs0MHB4XSB3LVs0MHB4XSBiZy10aGVtZS1hY2NlbnQtcHJpbWFyeSB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCBzaGFkb3ctbWQgaG92ZXI6YmctdGhlbWUtYWNjZW50LWhvdmVyIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJoLTUgdy01XCIgc3Ryb2tlV2lkdGg9ezIuNX0gLz5cclxuICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgPC9mb3JtPlxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00XCI+XHJcbiAgICAgICAge3NlYXJjaFF1ZXJ5LnRyaW0oKSAhPT0gXCJcIiAmJiAoXHJcbiAgICAgICAgICA8UHJvZHVjdEluZmluaXRlU2Nyb2xsaW5nXHJcbiAgICAgICAgICAgIHByb2R1Y3RzPXtwcm9kdWN0c31cclxuICAgICAgICAgICAgaXNMb2FkaW5nPXtsb2FkaW5nfVxyXG4gICAgICAgICAgICBvYnNlcnZlclRhcmdldD17b2JzZXJ2ZXJUYXJnZXR9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuIl0sIm5hbWVzIjpbIk1BSU5fVVJMIiwiUFJPRFVDVFMiLCJ1c2VOZXdBcGkiLCJTZWFyY2giLCJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwiUHJvZHVjdEluZmluaXRlU2Nyb2xsaW5nIiwidXNlSW5maW5pdGVTY3JvbGwiLCJTZWFyY2hCdG4iLCJzZWFyY2hRdWVyeSIsInNldFNlYXJjaFF1ZXJ5IiwiY3VycmVudFF1ZXJ5Iiwic2V0Q3VycmVudFF1ZXJ5IiwicGFnZSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2V0SGFzTW9yZSIsInByb2R1Y3RzIiwic2V0UHJvZHVjdHMiLCJnZXQiLCJiYXNlVVJMIiwib2JzZXJ2ZXJUYXJnZXQiLCJoYW5kbGVQcm9kdWN0U2VhcmNoIiwiaXNOZXdTZWFyY2giLCJ1cmwiLCJyZXNwb25zZSIsImRhdGEiLCJuZXh0IiwiQm9vbGVhbiIsInJlc3VsdHMiLCJsZW5ndGgiLCJwcmV2IiwiZXhpc3RpbmdJZHMiLCJTZXQiLCJtYXAiLCJwcm9kdWN0IiwiaWQiLCJuZXdQcm9kdWN0cyIsImZpbHRlciIsImhhcyIsImVycm9yIiwiaGFuZGxlciIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJkaXYiLCJjbGFzc05hbWUiLCJmb3JtIiwib25TdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJ0cmltIiwiaW5wdXQiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwiYnV0dG9uIiwic3Ryb2tlV2lkdGgiLCJpc0xvYWRpbmciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/utils/SearchBtn.tsx\n"));

/***/ })

});