
#23 24.75 ==================================== ERRORS ====================================
2025-May-26 09:22:08.030212
#23 24.75 _ ERROR at setup of TestEmailNotifications.test_send_order_confirmation_email __
2025-May-26 09:22:08.030212
#23 24.75
2025-May-26 09:22:08.204447
#23 24.75 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.204447
#23 24.75 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.204447
#23 24.75 params = None
2025-May-26 09:22:08.204447
#23 24.75 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.204447
#23 24.75
2025-May-26 09:22:08.228902
#23 24.75     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.75         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.75         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.75         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.75             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.75         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.75         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.75             if params is None:
2025-May-26 09:22:08.228902
#23 24.75                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.75 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.75 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.75
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.228902
#23 24.75
2025-May-26 09:22:08.228902
#23 24.75 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.228902
#23 24.75
2025-May-26 09:22:08.228902
#23 24.75 request = <SubRequest '_django_db_marker' for <Function test_send_order_confirmation_email>>
2025-May-26 09:22:08.228902
#23 24.75
2025-May-26 09:22:08.228902
#23 24.75     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.228902
#23 24.75     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.228902
#23 24.75         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.228902
#23 24.75         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.228902
#23 24.75         if marker:
2025-May-26 09:22:08.228902
#23 24.75 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.75
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.228902
#23 24.75 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.228902
#23 24.75     db_cfg = setup_databases(
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.228902
#23 24.75     connection.creation.create_test_db(
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.228902
#23 24.75     call_command(
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.228902
#23 24.75     return command.execute(*args, **defaults)
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.228902
#23 24.75     output = self.handle(*args, **options)
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.228902
#23 24.75     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.228902
#23 24.75     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.228902
#23 24.75     with connection.schema_editor() as editor:
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.228902
#23 24.75     self.execute(sql)
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.228902
#23 24.75     return super().execute(sql, None)
2025-May-26 09:22:08.228902
#23 24.75 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.228902
#23 24.76     cursor.execute(sql, params)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.228902
#23 24.76     return self._execute_with_wrappers(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.228902
#23 24.76     return executor(sql, params, many, context)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.228902
#23 24.76     with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.228902
#23 24.76 ___ ERROR at setup of TestEmailNotifications.test_send_payment_success_email ___
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 request = <SubRequest '_django_db_marker' for <Function test_send_payment_success_email>>
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.228902
#23 24.76     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.228902
#23 24.76         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.228902
#23 24.76         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.228902
#23 24.76         if marker:
2025-May-26 09:22:08.228902
#23 24.76 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.228902
#23 24.76     db_cfg = setup_databases(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.228902
#23 24.76     connection.creation.create_test_db(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.228902
#23 24.76     call_command(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.228902
#23 24.76     return command.execute(*args, **defaults)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.228902
#23 24.76     output = self.handle(*args, **options)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.228902
#23 24.76     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.228902
#23 24.76     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.228902
#23 24.76     with connection.schema_editor() as editor:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     self.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.228902
#23 24.76     return super().execute(sql, None)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.228902
#23 24.76     cursor.execute(sql, params)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.228902
#23 24.76     return self._execute_with_wrappers(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.228902
#23 24.76     return executor(sql, params, many, context)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.228902
#23 24.76     with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.228902
#23 24.76 ___ ERROR at setup of TestEmailNotifications.test_send_payment_failure_email ___
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 request = <SubRequest '_django_db_marker' for <Function test_send_payment_failure_email>>
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.228902
#23 24.76     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.228902
#23 24.76         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.228902
#23 24.76         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.228902
#23 24.76         if marker:
2025-May-26 09:22:08.228902
#23 24.76 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.228902
#23 24.76     db_cfg = setup_databases(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.228902
#23 24.76     connection.creation.create_test_db(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.228902
#23 24.76     call_command(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.228902
#23 24.76     return command.execute(*args, **defaults)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.228902
#23 24.76     output = self.handle(*args, **options)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.228902
#23 24.76     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.228902
#23 24.76     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.228902
#23 24.76     with connection.schema_editor() as editor:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     self.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.228902
#23 24.76     return super().execute(sql, None)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.228902
#23 24.76     cursor.execute(sql, params)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.228902
#23 24.76     return self._execute_with_wrappers(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.228902
#23 24.76     return executor(sql, params, many, context)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.228902
#23 24.76     with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.228902
#23 24.76 ______ ERROR at setup of TestEmailNotifications.test_email_error_handling ______
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 request = <SubRequest '_django_db_marker' for <Function test_email_error_handling>>
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.228902
#23 24.76     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.228902
#23 24.76         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.228902
#23 24.76         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.228902
#23 24.76         if marker:
2025-May-26 09:22:08.228902
#23 24.76 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.228902
#23 24.76     db_cfg = setup_databases(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.228902
#23 24.76     connection.creation.create_test_db(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.228902
#23 24.76     call_command(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.228902
#23 24.76     return command.execute(*args, **defaults)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.228902
#23 24.76     output = self.handle(*args, **options)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.228902
#23 24.76     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.228902
#23 24.76     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.228902
#23 24.76     with connection.schema_editor() as editor:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     self.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.228902
#23 24.76     return super().execute(sql, None)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.228902
#23 24.76     cursor.execute(sql, params)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.228902
#23 24.76     return self._execute_with_wrappers(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.228902
#23 24.76     return executor(sql, params, many, context)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.228902
#23 24.76     with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.228902
#23 24.76 ___________ ERROR at setup of TestOrderProcessing.test_create_order ____________
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 request = <SubRequest '_django_db_marker' for <Function test_create_order>>
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.228902
#23 24.76     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.228902
#23 24.76         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.228902
#23 24.76         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.228902
#23 24.76         if marker:
2025-May-26 09:22:08.228902
#23 24.76 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.228902
#23 24.76     db_cfg = setup_databases(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.228902
#23 24.76     connection.creation.create_test_db(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.228902
#23 24.76     call_command(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.228902
#23 24.76     return command.execute(*args, **defaults)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.228902
#23 24.76     output = self.handle(*args, **options)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.228902
#23 24.76     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.228902
#23 24.76     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.228902
#23 24.76     with connection.schema_editor() as editor:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     self.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.228902
#23 24.76     return super().execute(sql, None)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.228902
#23 24.76     cursor.execute(sql, params)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.228902
#23 24.76     return self._execute_with_wrappers(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.228902
#23 24.76     return executor(sql, params, many, context)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.228902
#23 24.76     with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.228902
#23 24.76 ____ ERROR at setup of TestOrderProcessing.test_create_order_minimum_value _____
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 request = <SubRequest '_django_db_marker' for <Function test_create_order_minimum_value>>
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.228902
#23 24.76     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.228902
#23 24.76         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.228902
#23 24.76         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.228902
#23 24.76         if marker:
2025-May-26 09:22:08.228902
#23 24.76 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.228902
#23 24.76     db_cfg = setup_databases(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.228902
#23 24.76     connection.creation.create_test_db(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.228902
#23 24.76     call_command(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.228902
#23 24.76     return command.execute(*args, **defaults)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.228902
#23 24.76     output = self.handle(*args, **options)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.228902
#23 24.76     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.228902
#23 24.76     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.228902
#23 24.76     with connection.schema_editor() as editor:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     self.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.228902
#23 24.76     return super().execute(sql, None)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.228902
#23 24.76     cursor.execute(sql, params)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.228902
#23 24.76     return self._execute_with_wrappers(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.228902
#23 24.76     return executor(sql, params, many, context)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.228902
#23 24.76     with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.228902
#23 24.76 _________ ERROR at setup of TestOrderProcessing.test_get_order_details _________
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 request = <SubRequest '_django_db_marker' for <Function test_get_order_details>>
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.228902
#23 24.76     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.228902
#23 24.76         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.228902
#23 24.76         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.228902
#23 24.76         if marker:
2025-May-26 09:22:08.228902
#23 24.76 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.228902
#23 24.76     db_cfg = setup_databases(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.228902
#23 24.76     connection.creation.create_test_db(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.228902
#23 24.76     call_command(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.228902
#23 24.76     return command.execute(*args, **defaults)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.228902
#23 24.76     output = self.handle(*args, **options)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.228902
#23 24.76     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.228902
#23 24.76     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.228902
#23 24.76     with connection.schema_editor() as editor:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     self.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.228902
#23 24.76     return super().execute(sql, None)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.228902
#23 24.76     cursor.execute(sql, params)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.228902
#23 24.76     return self._execute_with_wrappers(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.228902
#23 24.76     return executor(sql, params, many, context)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.228902
#23 24.76     with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.228902
#23 24.76 ________ ERROR at setup of TestOrderProcessing.test_update_order_status ________
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 request = <SubRequest '_django_db_marker' for <Function test_update_order_status>>
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.228902
#23 24.76     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.228902
#23 24.76         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.228902
#23 24.76         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.228902
#23 24.76         if marker:
2025-May-26 09:22:08.228902
#23 24.76 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.228902
#23 24.76     db_cfg = setup_databases(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.228902
#23 24.76     connection.creation.create_test_db(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.228902
#23 24.76     call_command(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.228902
#23 24.76     return command.execute(*args, **defaults)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.228902
#23 24.76     output = self.handle(*args, **options)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.228902
#23 24.76     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.228902
#23 24.76     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.228902
#23 24.76     with connection.schema_editor() as editor:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     self.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.228902
#23 24.76     return super().execute(sql, None)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.228902
#23 24.76     cursor.execute(sql, params)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.228902
#23 24.76     return self._execute_with_wrappers(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.228902
#23 24.76     return executor(sql, params, many, context)
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.228902
#23 24.76     with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.228902
#23 24.76     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.228902
#23 24.76 ___________ ERROR at setup of TestOrderProcessing.test_cancel_order ____________
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.76 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.76 params = None
2025-May-26 09:22:08.228902
#23 24.76 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.76         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.76         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.76         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.76             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.76         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.76         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.76             if params is None:
2025-May-26 09:22:08.228902
#23 24.76                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.76 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.76 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 request = <SubRequest '_django_db_marker' for <Function test_cancel_order>>
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.228902
#23 24.76     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.228902
#23 24.76         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.228902
#23 24.76         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.228902
#23 24.76         if marker:
2025-May-26 09:22:08.228902
#23 24.76 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.228902
#23 24.76 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.76     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.228902
#23 24.76     db_cfg = setup_databases(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.228902
#23 24.76     connection.creation.create_test_db(
2025-May-26 09:22:08.228902
#23 24.76 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.228902
#23 24.77     call_command(
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.228902
#23 24.77     return command.execute(*args, **defaults)
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.228902
#23 24.77     output = self.handle(*args, **options)
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.228902
#23 24.77     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.228902
#23 24.77     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.228902
#23 24.77     with connection.schema_editor() as editor:
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.228902
#23 24.77     self.execute(sql)
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.228902
#23 24.77     return super().execute(sql, None)
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.228902
#23 24.77     cursor.execute(sql, params)
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.228902
#23 24.77     return self._execute_with_wrappers(
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.228902
#23 24.77     return executor(sql, params, many, context)
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.228902
#23 24.77     with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.228902
#23 24.77     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.228902
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.77
2025-May-26 09:22:08.228902
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.77 params = None
2025-May-26 09:22:08.228902
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.77
2025-May-26 09:22:08.228902
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.77             if params is None:
2025-May-26 09:22:08.228902
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.77 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.77
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.228902
#23 24.77 _________ ERROR at setup of TestOrderProcessing.test_list_user_orders __________
2025-May-26 09:22:08.228902
#23 24.77
2025-May-26 09:22:08.228902
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.228902
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.228902
#23 24.77 params = None
2025-May-26 09:22:08.228902
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.228902
#23 24.77
2025-May-26 09:22:08.228902
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.228902
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.228902
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.228902
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.228902
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.228902
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.228902
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.228902
#23 24.77             if params is None:
2025-May-26 09:22:08.228902
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.228902
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.228902
#23 24.77 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.228902
#23 24.77
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.228902
#23 24.77
2025-May-26 09:22:08.228902
#23 24.77 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.228902
#23 24.77
2025-May-26 09:22:08.228902
#23 24.77 request = <SubRequest '_django_db_marker' for <Function test_list_user_orders>>
2025-May-26 09:22:08.228902
#23 24.77
2025-May-26 09:22:08.228902
#23 24.77     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.228902
#23 24.77     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.228902
#23 24.77         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.228902
#23 24.77         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.228902
#23 24.77         if marker:
2025-May-26 09:22:08.228902
#23 24.77 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.77
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.228902
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.228902
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.228902
#23 24.77
2025-May-26 09:22:08.241506
request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.241506
#23 24.77     db_cfg = setup_databases(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.241506
#23 24.77     connection.creation.create_test_db(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.241506
#23 24.77     call_command(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.241506
#23 24.77     return command.execute(*args, **defaults)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.241506
#23 24.77     output = self.handle(*args, **options)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.241506
#23 24.77     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.241506
#23 24.77     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.241506
#23 24.77     with connection.schema_editor() as editor:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     self.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.241506
#23 24.77     return super().execute(sql, None)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.241506
#23 24.77     cursor.execute(sql, params)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.241506
#23 24.77     return self._execute_with_wrappers(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.241506
#23 24.77     return executor(sql, params, many, context)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.241506
#23 24.77     with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.241506
#23 24.77 ________ ERROR at setup of TestPaymentProcessing.test_initiate_payment _________
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 request = <SubRequest '_django_db_marker' for <Function test_initiate_payment>>
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.241506
#23 24.77     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.241506
#23 24.77         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.241506
#23 24.77         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.241506
#23 24.77         if marker:
2025-May-26 09:22:08.241506
#23 24.77 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.241506
#23 24.77     db_cfg = setup_databases(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.241506
#23 24.77     connection.creation.create_test_db(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.241506
#23 24.77     call_command(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.241506
#23 24.77     return command.execute(*args, **defaults)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.241506
#23 24.77     output = self.handle(*args, **options)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.241506
#23 24.77     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.241506
#23 24.77     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.241506
#23 24.77     with connection.schema_editor() as editor:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     self.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.241506
#23 24.77     return super().execute(sql, None)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.241506
#23 24.77     cursor.execute(sql, params)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.241506
#23 24.77     return self._execute_with_wrappers(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.241506
#23 24.77     return executor(sql, params, many, context)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.241506
#23 24.77     with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.241506
#23 24.77 __ ERROR at setup of TestPaymentProcessing.test_initiate_payment_already_paid __
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 request = <SubRequest '_django_db_marker' for <Function test_initiate_payment_already_paid>>
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.241506
#23 24.77     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.241506
#23 24.77         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.241506
#23 24.77         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.241506
#23 24.77         if marker:
2025-May-26 09:22:08.241506
#23 24.77 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.241506
#23 24.77     db_cfg = setup_databases(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.241506
#23 24.77     connection.creation.create_test_db(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.241506
#23 24.77     call_command(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.241506
#23 24.77     return command.execute(*args, **defaults)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.241506
#23 24.77     output = self.handle(*args, **options)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.241506
#23 24.77     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.241506
#23 24.77     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.241506
#23 24.77     with connection.schema_editor() as editor:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     self.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.241506
#23 24.77     return super().execute(sql, None)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.241506
#23 24.77     cursor.execute(sql, params)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.241506
#23 24.77     return self._execute_with_wrappers(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.241506
#23 24.77     return executor(sql, params, many, context)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.241506
#23 24.77     with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.241506
#23 24.77 ____ ERROR at setup of TestPaymentProcessing.test_payment_callback_success _____
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 request = <SubRequest '_django_db_marker' for <Function test_payment_callback_success>>
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.241506
#23 24.77     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.241506
#23 24.77         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.241506
#23 24.77         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.241506
#23 24.77         if marker:
2025-May-26 09:22:08.241506
#23 24.77 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.241506
#23 24.77     db_cfg = setup_databases(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.241506
#23 24.77     connection.creation.create_test_db(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.241506
#23 24.77     call_command(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.241506
#23 24.77     return command.execute(*args, **defaults)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.241506
#23 24.77     output = self.handle(*args, **options)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.241506
#23 24.77     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.241506
#23 24.77     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.241506
#23 24.77     with connection.schema_editor() as editor:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     self.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.241506
#23 24.77     return super().execute(sql, None)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.241506
#23 24.77     cursor.execute(sql, params)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.241506
#23 24.77     return self._execute_with_wrappers(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.241506
#23 24.77     return executor(sql, params, many, context)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.241506
#23 24.77     with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.241506
#23 24.77 ____ ERROR at setup of TestPaymentProcessing.test_payment_callback_failure _____
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 request = <SubRequest '_django_db_marker' for <Function test_payment_callback_failure>>
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.241506
#23 24.77     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.241506
#23 24.77         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.241506
#23 24.77         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.241506
#23 24.77         if marker:
2025-May-26 09:22:08.241506
#23 24.77 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.241506
#23 24.77     db_cfg = setup_databases(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.241506
#23 24.77     connection.creation.create_test_db(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.241506
#23 24.77     call_command(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.241506
#23 24.77     return command.execute(*args, **defaults)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.241506
#23 24.77     output = self.handle(*args, **options)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.241506
#23 24.77     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.241506
#23 24.77     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.241506
#23 24.77     with connection.schema_editor() as editor:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     self.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.241506
#23 24.77     return super().execute(sql, None)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.241506
#23 24.77     cursor.execute(sql, params)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.241506
#23 24.77     return self._execute_with_wrappers(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.241506
#23 24.77     return executor(sql, params, many, context)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.241506
#23 24.77     with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.241506
#23 24.77 _______ ERROR at setup of TestPaymentProcessing.test_webhook_processing ________
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 request = <SubRequest '_django_db_marker' for <Function test_webhook_processing>>
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.241506
#23 24.77     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.241506
#23 24.77         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.241506
#23 24.77         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.241506
#23 24.77         if marker:
2025-May-26 09:22:08.241506
#23 24.77 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.241506
#23 24.77     db_cfg = setup_databases(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.241506
#23 24.77     connection.creation.create_test_db(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.241506
#23 24.77     call_command(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.241506
#23 24.77     return command.execute(*args, **defaults)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.241506
#23 24.77     output = self.handle(*args, **options)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.241506
#23 24.77     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.241506
#23 24.77     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.241506
#23 24.77     with connection.schema_editor() as editor:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     self.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.241506
#23 24.77     return super().execute(sql, None)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.241506
#23 24.77     cursor.execute(sql, params)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.241506
#23 24.77     return self._execute_with_wrappers(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.241506
#23 24.77     return executor(sql, params, many, context)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.241506
#23 24.77     with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.241506
#23 24.77 __________ ERROR at setup of TestPhonePeService.test_initiate_payment __________
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 request = <SubRequest '_django_db_marker' for <Function test_initiate_payment>>
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.241506
#23 24.77     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.241506
#23 24.77         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.241506
#23 24.77         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.241506
#23 24.77         if marker:
2025-May-26 09:22:08.241506
#23 24.77 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.77     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.241506
#23 24.77     db_cfg = setup_databases(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.241506
#23 24.77     connection.creation.create_test_db(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.241506
#23 24.77     call_command(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.241506
#23 24.77     return command.execute(*args, **defaults)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.241506
#23 24.77     output = self.handle(*args, **options)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.241506
#23 24.77     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.241506
#23 24.77     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.241506
#23 24.77     with connection.schema_editor() as editor:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     self.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.241506
#23 24.77     return super().execute(sql, None)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.241506
#23 24.77     cursor.execute(sql, params)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.241506
#23 24.77     return self._execute_with_wrappers(
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.241506
#23 24.77     return executor(sql, params, many, context)
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.241506
#23 24.77     with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.241506
#23 24.77     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.241506
#23 24.77 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.77 params = None
2025-May-26 09:22:08.241506
#23 24.77 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.77         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.77         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.77         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.77             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.77         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.77         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.77             if params is None:
2025-May-26 09:22:08.241506
#23 24.77                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.77 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.77 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.241506
#23 24.77 _______ ERROR at setup of TestPhonePeService.test_initiate_payment_error _______
2025-May-26 09:22:08.241506
#23 24.77
2025-May-26 09:22:08.241506
#23 24.77 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.77 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.78 params = None
2025-May-26 09:22:08.241506
#23 24.78 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.78         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.78         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.78         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.78             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.78         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.78         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.78             if params is None:
2025-May-26 09:22:08.241506
#23 24.78                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.78 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.78 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78 request = <SubRequest '_django_db_marker' for <Function test_initiate_payment_error>>
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.241506
#23 24.78     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.241506
#23 24.78         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.241506
#23 24.78         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.241506
#23 24.78         if marker:
2025-May-26 09:22:08.241506
#23 24.78 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.241506
#23 24.78 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.241506
#23 24.78     db_cfg = setup_databases(
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.241506
#23 24.78     connection.creation.create_test_db(
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.241506
#23 24.78     call_command(
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.241506
#23 24.78     return command.execute(*args, **defaults)
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.241506
#23 24.78     output = self.handle(*args, **options)
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.241506
#23 24.78     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.241506
#23 24.78     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.241506
#23 24.78     with connection.schema_editor() as editor:
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.241506
#23 24.78     self.execute(sql)
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.241506
#23 24.78     return super().execute(sql, None)
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.241506
#23 24.78     cursor.execute(sql, params)
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.241506
#23 24.78     return self._execute_with_wrappers(
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.241506
#23 24.78     return executor(sql, params, many, context)
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.241506
#23 24.78     with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.241506
#23 24.78     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.241506
#23 24.78 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.78 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.78 params = None
2025-May-26 09:22:08.241506
#23 24.78 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.78         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.78         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.78         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.78             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.78         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.78         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.78             if params is None:
2025-May-26 09:22:08.241506
#23 24.78                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.78 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.78 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.241506
#23 24.78 _ ERROR at setup of TestStabilityRobustness.test_phonepe_service_initialization_failure _
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.241506
#23 24.78 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.241506
#23 24.78 params = None
2025-May-26 09:22:08.241506
#23 24.78 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.241506
#23 24.78         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.241506
#23 24.78         # ever set during testing).
2025-May-26 09:22:08.241506
#23 24.78         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.241506
#23 24.78             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.241506
#23 24.78         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.241506
#23 24.78         with self.db.wrap_database_errors:
2025-May-26 09:22:08.241506
#23 24.78             if params is None:
2025-May-26 09:22:08.241506
#23 24.78                 # params default might be backend specific.
2025-May-26 09:22:08.241506
#23 24.78 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.241506
#23 24.78 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78 request = <SubRequest '_django_db_marker' for <Function test_phonepe_service_initialization_failure>>
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.241506
#23 24.78     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.241506
#23 24.78         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.241506
#23 24.78         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.241506
#23 24.78         if marker:
2025-May-26 09:22:08.241506
#23 24.78 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.241506
#23 24.78 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.241506
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.241506
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plu
2025-May-26 09:22:08.256023
gin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.256023
#23 24.78     db_cfg = setup_databases(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.256023
#23 24.78     connection.creation.create_test_db(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.256023
#23 24.78     call_command(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.256023
#23 24.78     return command.execute(*args, **defaults)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.256023
#23 24.78     output = self.handle(*args, **options)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.256023
#23 24.78     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.256023
#23 24.78     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.256023
#23 24.78     with connection.schema_editor() as editor:
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.256023
#23 24.78     self.execute(sql)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.256023
#23 24.78     return super().execute(sql, None)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.256023
#23 24.78     cursor.execute(sql, params)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.256023
#23 24.78     return self._execute_with_wrappers(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.256023
#23 24.78     return executor(sql, params, many, context)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.256023
#23 24.78     with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.256023
#23 24.78     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.256023
#23 24.78 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.78 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.78 params = None
2025-May-26 09:22:08.256023
#23 24.78 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.78         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.78         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.78         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.78             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.78         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.78         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.78             if params is None:
2025-May-26 09:22:08.256023
#23 24.78                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.78 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.78 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.256023
#23 24.78 _ ERROR at setup of TestStabilityRobustness.test_phonepe_payment_initiation_failure _
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.78 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.78 params = None
2025-May-26 09:22:08.256023
#23 24.78 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.78         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.78         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.78         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.78             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.78         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.78         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.78             if params is None:
2025-May-26 09:22:08.256023
#23 24.78                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.78 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.78 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 request = <SubRequest '_django_db_marker' for <Function test_phonepe_payment_initiation_failure>>
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.256023
#23 24.78     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.256023
#23 24.78         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.256023
#23 24.78         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.256023
#23 24.78         if marker:
2025-May-26 09:22:08.256023
#23 24.78 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.256023
#23 24.78 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.256023
#23 24.78     db_cfg = setup_databases(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.256023
#23 24.78     connection.creation.create_test_db(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.256023
#23 24.78     call_command(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.256023
#23 24.78     return command.execute(*args, **defaults)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.256023
#23 24.78     output = self.handle(*args, **options)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.256023
#23 24.78     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.256023
#23 24.78     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.256023
#23 24.78     with connection.schema_editor() as editor:
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.256023
#23 24.78     self.execute(sql)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.256023
#23 24.78     return super().execute(sql, None)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.256023
#23 24.78     cursor.execute(sql, params)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.256023
#23 24.78     return self._execute_with_wrappers(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.256023
#23 24.78     return executor(sql, params, many, context)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.256023
#23 24.78     with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.256023
#23 24.78     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.256023
#23 24.78 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.78 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.78 params = None
2025-May-26 09:22:08.256023
#23 24.78 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.78         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.78         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.78         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.78             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.78         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.78         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.78             if params is None:
2025-May-26 09:22:08.256023
#23 24.78                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.78 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.78 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.256023
#23 24.78 _ ERROR at setup of TestStabilityRobustness.test_invalid_order_id_in_callback __
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.78 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.78 params = None
2025-May-26 09:22:08.256023
#23 24.78 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.78         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.78         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.78         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.78             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.78         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.78         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.78             if params is None:
2025-May-26 09:22:08.256023
#23 24.78                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.78 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.78 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 request = <SubRequest '_django_db_marker' for <Function test_invalid_order_id_in_callback>>
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.256023
#23 24.78     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.256023
#23 24.78         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.256023
#23 24.78         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.256023
#23 24.78         if marker:
2025-May-26 09:22:08.256023
#23 24.78 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.256023
#23 24.78 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.256023
#23 24.78     db_cfg = setup_databases(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.256023
#23 24.78     connection.creation.create_test_db(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.256023
#23 24.78     call_command(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.256023
#23 24.78     return command.execute(*args, **defaults)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.256023
#23 24.78     output = self.handle(*args, **options)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.256023
#23 24.78     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.256023
#23 24.78     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.256023
#23 24.78     with connection.schema_editor() as editor:
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.256023
#23 24.78     self.execute(sql)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.256023
#23 24.78     return super().execute(sql, None)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.256023
#23 24.78     cursor.execute(sql, params)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.256023
#23 24.78     return self._execute_with_wrappers(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.256023
#23 24.78     return executor(sql, params, many, context)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.256023
#23 24.78     with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.256023
#23 24.78     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.256023
#23 24.78 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.78 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.78 params = None
2025-May-26 09:22:08.256023
#23 24.78 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.78         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.78         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.78         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.78             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.78         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.78         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.78             if params is None:
2025-May-26 09:22:08.256023
#23 24.78                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.78 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.78 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.256023
#23 24.78 _ ERROR at setup of TestStabilityRobustness.test_missing_order_id_in_callback __
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.78 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.78 params = None
2025-May-26 09:22:08.256023
#23 24.78 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.78         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.78         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.78         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.78             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.78         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.78         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.78             if params is None:
2025-May-26 09:22:08.256023
#23 24.78                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.78 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.78 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 request = <SubRequest '_django_db_marker' for <Function test_missing_order_id_in_callback>>
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.256023
#23 24.78     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.256023
#23 24.78         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.256023
#23 24.78         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.256023
#23 24.78         if marker:
2025-May-26 09:22:08.256023
#23 24.78 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.256023
#23 24.78 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.78     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.256023
#23 24.78     db_cfg = setup_databases(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.256023
#23 24.78     connection.creation.create_test_db(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.256023
#23 24.78     call_command(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.256023
#23 24.78     return command.execute(*args, **defaults)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.256023
#23 24.78     output = self.handle(*args, **options)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.256023
#23 24.78     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.256023
#23 24.78     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.256023
#23 24.78     with connection.schema_editor() as editor:
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.256023
#23 24.78     self.execute(sql)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.256023
#23 24.78     return super().execute(sql, None)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.256023
#23 24.78     cursor.execute(sql, params)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.256023
#23 24.78     return self._execute_with_wrappers(
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.256023
#23 24.78     return executor(sql, params, many, context)
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.256023
#23 24.78     with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.256023
#23 24.78     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.256023
#23 24.78 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.78 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.78 params = None
2025-May-26 09:22:08.256023
#23 24.78 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.78         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.78         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.78         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.78             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.78         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.78         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.78             if params is None:
2025-May-26 09:22:08.256023
#23 24.78                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.78 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.78 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.78 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.256023
#23 24.78 _ ERROR at setup of TestStabilityRobustness.test_invalid_signature_in_webhook __
2025-May-26 09:22:08.256023
#23 24.78
2025-May-26 09:22:08.256023
#23 24.79 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.79 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.79 params = None
2025-May-26 09:22:08.256023
#23 24.79 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.79         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.79         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.79         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.79             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.79         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.79         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.79             if params is None:
2025-May-26 09:22:08.256023
#23 24.79                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.79 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.79 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 request = <SubRequest '_django_db_marker' for <Function test_invalid_signature_in_webhook>>
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.256023
#23 24.79     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.256023
#23 24.79         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.256023
#23 24.79         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.256023
#23 24.79         if marker:
2025-May-26 09:22:08.256023
#23 24.79 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.256023
#23 24.79 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.256023
#23 24.79     db_cfg = setup_databases(
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.256023
#23 24.79     connection.creation.create_test_db(
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.256023
#23 24.79     call_command(
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.256023
#23 24.79     return command.execute(*args, **defaults)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.256023
#23 24.79     output = self.handle(*args, **options)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.256023
#23 24.79     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.256023
#23 24.79     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.256023
#23 24.79     with connection.schema_editor() as editor:
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.256023
#23 24.79     self.execute(sql)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.256023
#23 24.79     return super().execute(sql, None)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.256023
#23 24.79     cursor.execute(sql, params)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.256023
#23 24.79     return self._execute_with_wrappers(
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.256023
#23 24.79     return executor(sql, params, many, context)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.256023
#23 24.79     with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.256023
#23 24.79     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.256023
#23 24.79 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.79 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.79 params = None
2025-May-26 09:22:08.256023
#23 24.79 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.79         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.79         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.79         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.79             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.79         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.79         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.79             if params is None:
2025-May-26 09:22:08.256023
#23 24.79                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.79 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.79 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.256023
#23 24.79 _ ERROR at setup of TestStabilityRobustness.test_missing_signature_in_webhook __
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.79 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.79 params = None
2025-May-26 09:22:08.256023
#23 24.79 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.79         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.79         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.79         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.79             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.79         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.79         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.79             if params is None:
2025-May-26 09:22:08.256023
#23 24.79                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.79 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.79 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 request = <SubRequest '_django_db_marker' for <Function test_missing_signature_in_webhook>>
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.256023
#23 24.79     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.256023
#23 24.79         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.256023
#23 24.79         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.256023
#23 24.79         if marker:
2025-May-26 09:22:08.256023
#23 24.79 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.256023
#23 24.79 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.256023
#23 24.79     db_cfg = setup_databases(
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.256023
#23 24.79     connection.creation.create_test_db(
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.256023
#23 24.79     call_command(
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.256023
#23 24.79     return command.execute(*args, **defaults)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.256023
#23 24.79     output = self.handle(*args, **options)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.256023
#23 24.79     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.256023
#23 24.79     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.256023
#23 24.79     with connection.schema_editor() as editor:
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.256023
#23 24.79     self.execute(sql)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.256023
#23 24.79     return super().execute(sql, None)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.256023
#23 24.79     cursor.execute(sql, params)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.256023
#23 24.79     return self._execute_with_wrappers(
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.256023
#23 24.79     return executor(sql, params, many, context)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.256023
#23 24.79     with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.256023
#23 24.79     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.256023
#23 24.79 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.79 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.79 params = None
2025-May-26 09:22:08.256023
#23 24.79 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.79         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.79         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.79         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.79             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.79         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.79         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.79             if params is None:
2025-May-26 09:22:08.256023
#23 24.79                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.79 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.79 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.256023
#23 24.79 _ ERROR at setup of TestStabilityRobustness.test_invalid_transaction_id_in_webhook _
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.79 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.79 params = None
2025-May-26 09:22:08.256023
#23 24.79 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.79         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.79         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.79         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.79             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.79         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.79         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.79             if params is None:
2025-May-26 09:22:08.256023
#23 24.79                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.79 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.79 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 request = <SubRequest '_django_db_marker' for <Function test_invalid_transaction_id_in_webhook>>
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.256023
#23 24.79     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.256023
#23 24.79         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.256023
#23 24.79         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.256023
#23 24.79         if marker:
2025-May-26 09:22:08.256023
#23 24.79 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.256023
#23 24.79 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.256023
#23 24.79     db_cfg = setup_databases(
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.256023
#23 24.79     connection.creation.create_test_db(
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.256023
#23 24.79     call_command(
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.256023
#23 24.79     return command.execute(*args, **defaults)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.256023
#23 24.79     output = self.handle(*args, **options)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.256023
#23 24.79     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.256023
#23 24.79     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.256023
#23 24.79     with connection.schema_editor() as editor:
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.256023
#23 24.79     self.execute(sql)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.256023
#23 24.79     return super().execute(sql, None)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.256023
#23 24.79     cursor.execute(sql, params)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.256023
#23 24.79     return self._execute_with_wrappers(
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.256023
#23 24.79     return executor(sql, params, many, context)
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.256023
#23 24.79     with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.256023
#23 24.79     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.256023
#23 24.79 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.79 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.79 params = None
2025-May-26 09:22:08.256023
#23 24.79 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.79         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.79         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.79         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.79             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.79         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.79         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.79             if params is None:
2025-May-26 09:22:08.256023
#23 24.79                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.79 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.79 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.256023
#23 24.79 _ ERROR at setup of TestStabilityRobustness.test_concurrent_payment_processing _
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.256023
#23 24.79 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.256023
#23 24.79 params = None
2025-May-26 09:22:08.256023
#23 24.79 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.256023
#23 24.79         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.256023
#23 24.79         # ever set during testing).
2025-May-26 09:22:08.256023
#23 24.79         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.256023
#23 24.79             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.256023
#23 24.79         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.256023
#23 24.79         with self.db.wrap_database_errors:
2025-May-26 09:22:08.256023
#23 24.79             if params is None:
2025-May-26 09:22:08.256023
#23 24.79                 # params default might be backend specific.
2025-May-26 09:22:08.256023
#23 24.79 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.256023
#23 24.79 E               psycopg2.errors.UndefinedTable: relation "auth_group" does not exist
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: UndefinedTable
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 The above exception was the direct cause of the following exception:
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 request = <SubRequest '_django_db_marker' for <Function test_concurrent_payment_processing>>
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79     @pytest.fixture(autouse=True)
2025-May-26 09:22:08.256023
#23 24.79     def _django_db_marker(request: pytest.FixtureRequest) -> None:
2025-May-26 09:22:08.256023
#23 24.79         """Implement the django_db marker, internal to pytest-django."""
2025-May-26 09:22:08.256023
#23 24.79         marker = request.node.get_closest_marker("django_db")
2025-May-26 09:22:08.256023
#23 24.79         if marker:
2025-May-26 09:22:08.256023
#23 24.79 >           request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527:
2025-May-26 09:22:08.256023
#23 24.79 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.256023
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.256023
#23 24.79     req
2025-May-26 09:22:08.273399
uest.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/plugin.py:527: in _django_db_marker
2025-May-26 09:22:08.273399
#23 24.79     request.getfixturevalue("_django_db_helper")
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/pytest_django/fixtures.py:140: in django_db_setup
2025-May-26 09:22:08.273399
#23 24.79     db_cfg = setup_databases(
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/test/utils.py:203: in setup_databases
2025-May-26 09:22:08.273399
#23 24.79     connection.creation.create_test_db(
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/creation.py:78: in create_test_db
2025-May-26 09:22:08.273399
#23 24.79     call_command(
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/__init__.py:194: in call_command
2025-May-26 09:22:08.273399
#23 24.79     return command.execute(*args, **defaults)
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:459: in execute
2025-May-26 09:22:08.273399
#23 24.79     output = self.handle(*args, **options)
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/base.py:107: in wrapper
2025-May-26 09:22:08.273399
#23 24.79     res = handle_func(*args, **kwargs)
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:321: in handle
2025-May-26 09:22:08.273399
#23 24.79     self.sync_apps(connection, executor.loader.unmigrated_apps)
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/core/management/commands/migrate.py:468: in sync_apps
2025-May-26 09:22:08.273399
#23 24.79     with connection.schema_editor() as editor:
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:166: in __exit__
2025-May-26 09:22:08.273399
#23 24.79     self.execute(sql)
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/postgresql/schema.py:48: in execute
2025-May-26 09:22:08.273399
#23 24.79     return super().execute(sql, None)
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/base/schema.py:201: in execute
2025-May-26 09:22:08.273399
#23 24.79     cursor.execute(sql, params)
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:79: in execute
2025-May-26 09:22:08.273399
#23 24.79     return self._execute_with_wrappers(
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:92: in _execute_with_wrappers
2025-May-26 09:22:08.273399
#23 24.79     return executor(sql, params, many, context)
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:100: in _execute
2025-May-26 09:22:08.273399
#23 24.79     with self.db.wrap_database_errors:
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/utils.py:91: in __exit__
2025-May-26 09:22:08.273399
#23 24.79     raise dj_exc_value.with_traceback(traceback) from exc_value
2025-May-26 09:22:08.273399
#23 24.79 _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
2025-May-26 09:22:08.273399
#23 24.79
2025-May-26 09:22:08.273399
#23 24.79 self = <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>
2025-May-26 09:22:08.273399
#23 24.79 sql = 'ALTER TABLE "users_customer_groups" ADD CONSTRAINT "users_customer_groups_group_id_0d28549d_fk_auth_group_id" FOREIGN KEY ("group_id") REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED'
2025-May-26 09:22:08.273399
#23 24.79 params = None
2025-May-26 09:22:08.273399
#23 24.79 ignored_wrapper_args = (False, {'connection': postgresql connection to default: <connection object at 0x777df8a49940; dsn: 'user=postgres pas...t=5432 client_encoding=UTF8', closed: 0>, 'cursor': <django.db.backends.utils.CursorWrapper object at 0x777df89932d0>})
2025-May-26 09:22:08.273399
#23 24.79
2025-May-26 09:22:08.273399
#23 24.79     def _execute(self, sql, params, *ignored_wrapper_args):
2025-May-26 09:22:08.273399
#23 24.79         # Raise a warning during app initialization (stored_app_configs is only
2025-May-26 09:22:08.273399
#23 24.79         # ever set during testing).
2025-May-26 09:22:08.273399
#23 24.79         if not apps.ready and not apps.stored_app_configs:
2025-May-26 09:22:08.273399
#23 24.79             warnings.warn(self.APPS_NOT_READY_WARNING_MSG, category=RuntimeWarning)
2025-May-26 09:22:08.273399
#23 24.79         self.db.validate_no_broken_transaction()
2025-May-26 09:22:08.273399
#23 24.79         with self.db.wrap_database_errors:
2025-May-26 09:22:08.273399
#23 24.79             if params is None:
2025-May-26 09:22:08.273399
#23 24.79                 # params default might be backend specific.
2025-May-26 09:22:08.273399
#23 24.79 >               return self.cursor.execute(sql)
2025-May-26 09:22:08.273399
#23 24.79 E               django.db.utils.ProgrammingError: relation "auth_group" does not exist
2025-May-26 09:22:08.273399
#23 24.79
2025-May-26 09:22:08.273399
#23 24.79 /opt/venv/lib/python3.11/site-packages/django/db/backends/utils.py:103: ProgrammingError
2025-May-26 09:22:08.273399
#23 24.79
2025-May-26 09:22:08.273399
#23 24.79 ---------- coverage: platform linux, python 3.11.10-final-0 ----------
2025-May-26 09:22:08.273399
#23 24.79 Name                                        Stmts   Miss  Cover
2025-May-26 09:22:08.273399
#23 24.79 ---------------------------------------------------------------
2025-May-26 09:22:08.273399
#23 24.79 backend/__init__.py                             0      0   100%
2025-May-26 09:22:08.273399
#23 24.79 backend/asgi.py                                 4      4     0%
2025-May-26 09:22:08.273399
#23 24.79 backend/cdn_settings.py                        22     22     0%
2025-May-26 09:22:08.273399
#23 24.79 backend/management/__init__.py                  0      0   100%
2025-May-26 09:22:08.273399
#23 24.79 backend/management/commands/__init__.py         0      0   100%
2025-May-26 09:22:08.273399
#23 24.79 backend/management/commands/test_redis.py      65     65     0%
2025-May-26 09:22:08.273399
#23 24.79 backend/minio_settings.py                      34     18    47%
2025-May-26 09:22:08.273399
#23 24.79 backend/settings.py                            93     18    81%
2025-May-26 09:22:08.273399
#23 24.79 backend/urls.py                                11     11     0%
2025-May-26 09:22:08.273399
#23 24.79 backend/wsgi.py                                 5      5     0%
2025-May-26 09:22:08.273399
#23 24.79 dashboard/__init__.py                           0      0   100%
2025-May-26 09:22:08.273399
#23 24.79 dashboard/admin.py                              1      0   100%
2025-May-26 09:22:08.273399
#23 24.79 dashboard/apps.py                               4      0   100%
2025-May-26 09:22:08.273399
#23 24.79 dashboard/models.py                             1      0   100%
2025-May-26 09:22:08.273399
#23 24.79 dashboard/serializers.py                       13     13     0%
2025-May-26 09:22:08.273399
#23 24.79 dashboard/tests.py                              1      1     0%
2025-May-26 09:22:08.273399
#23 24.79 dashboard/urls.py                               4      4     0%
2025-May-26 09:22:08.273399
#23 24.79 dashboard/views.py                             30     30     0%
2025-May-26 09:22:08.273399
#23 24.79 fix_db_image_paths.py                         102    102     0%
2025-May-26 09:22:08.273399
#23 24.79 fix_minio_paths.py                             91     91     0%
2025-May-26 09:22:08.273399
#23 24.79 load_products.py                               36     36     0%
2025-May-26 09:22:08.273399
#23 24.79 load_qubo.py                                   47     47     0%
2025-May-26 09:22:08.273399
#23 24.79 manage.py                                      11     11     0%
2025-May-26 09:22:08.273399
#23 24.79 new_loaddata.py                                45     45     0%
2025-May-26 09:22:08.273399
#23 24.79 orders/models.py                               99     12    88%
2025-May-26 09:22:08.273399
#23 24.79 orders/utils.py                                69     51    26%
2025-May-26 09:22:08.273399
#23 24.79 passenger_wsgi.py                               3      3     0%
2025-May-26 09:22:08.273399
#23 24.79 payment_gateway/__init__.py                     0      0   100%
2025-May-26 09:22:08.273399
#23 24.79 payment_gateway/admin.py                        1      0   100%
2025-May-26 09:22:08.273399
#23 24.79 payment_gateway/apps.py                         4      0   100%
2025-May-26 09:22:08.273399
#23 24.79 payment_gateway/models.py                       1      0   100%
2025-May-26 09:22:08.273399
#23 24.79 payment_gateway/services.py                    86     58    33%
2025-May-26 09:22:08.273399
#23 24.79 payment_gateway/tests.py                        1      1     0%
2025-May-26 09:22:08.273399
#23 24.79 payment_gateway/urls.py                         4      4     0%
2025-May-26 09:22:08.273399
#23 24.79 payment_gateway/views.py                      157    157     0%
2025-May-26 09:22:08.273399
#23 24.79 product_generate.py                            25     25     0%
2025-May-26 09:22:08.273399
#23 24.79 products/apps.py                                6      0   100%
2025-May-26 09:22:08.273399
#23 24.79 products/models.py                            133     39    71%
2025-May-26 09:22:08.273399
#23 24.79 products/signals.py                            33     12    64%
2025-May-26 09:22:08.273399
#23 24.79 products/utils.py                              62     51    18%
2025-May-26 09:22:08.273399
#23 24.79 promotions/models.py                           48     12    75%
2025-May-26 09:22:08.273399
#23 24.79 redis_settings_example.py                      20     20     0%
2025-May-26 09:22:08.273399
#23 24.79 sync_media_to_minio.py                        115    115     0%
2025-May-26 09:22:08.273399
#23 24.79 test_redis.py                                  34     34     0%
2025-May-26 09:22:08.273399
#23 24.79 tests/__init__.py                               0      0   100%
2025-May-26 09:22:08.273399
#23 24.79 tests/conftest.py                              98     62    37%
2025-May-26 09:22:08.273399
#23 24.79 tests/test_configuration.py                    34      2    94%
2025-May-26 09:22:08.273399
#23 24.79 tests/test_email_notifications.py             101     60    41%
2025-May-26 09:22:08.273399
#23 24.79 tests/test_order_processing.py                100     81    19%
2025-May-26 09:22:08.273399
#23 24.79 tests/test_payment_processing.py              101     80    21%
2025-May-26 09:22:08.273399
#23 24.79 tests/test_phonepe_service.py                 100     39    61%
2025-May-26 09:22:08.273399
#23 24.79 tests/test_settings.py                         20      0   100%
2025-May-26 09:22:08.273399
#23 24.79 tests/test_stability_robustness.py             89     60    33%
2025-May-26 09:22:08.273399
#23 24.79 updatecategories.py                            56     56     0%
2025-May-26 09:22:08.273399
#23 24.79 users/admin.py                                 81     23    72%
2025-May-26 09:22:08.273399
#23 24.79 users/manager.py                               18     14    22%
2025-May-26 09:22:08.273399
#23 24.79 users/models.py                                77     11    86%
2025-May-26 09:22:08.273399
#23 24.79 users/utils.py                                 32     16    50%
2025-May-26 09:22:08.273399
#23 24.79 ---------------------------------------------------------------
2025-May-26 09:22:08.273399
#23 24.79 TOTAL                                        2428   1621    33%
2025-May-26 09:22:08.273399
#23 24.79
2025-May-26 09:22:08.273399
#23 24.79 =========================== short test summary info ============================
2025-May-26 09:22:08.273399
#23 24.79 ERROR tests/test_email_notifications.py::TestEmailNotifications::test_send_order_confirmation_email
2025-May-26 09:22:08.273399
#23 24.79 ERROR tests/test_email_notifications.py::TestEmailNotifications::test_send_payment_success_email
2025-May-26 09:22:08.273399
#23 24.79 ERROR tests/test_email_notifications.py::TestEmailNotifications::test_send_payment_failure_email
2025-May-26 09:22:08.273399
#23 24.79 ERROR tests/test_email_notifications.py::TestEmailNotifications::test_email_error_handling
2025-May-26 09:22:08.273399
#23 24.79 ERROR tests/test_order_processing.py::TestOrderProcessing::test_create_order
2025-May-26 09:22:08.273399
#23 24.79 ERROR tests/test_order_processing.py::TestOrderProcessing::test_create_order_minimum_value
2025-May-26 09:22:08.273399
#23 24.79 ERROR tests/test_order_processing.py::TestOrderProcessing::test_get_order_details
2025-May-26 09:22:08.273399
#23 24.79 ERROR tests/test_order_processing.py::TestOrderProcessing::test_update_order_status
2025-May-26 09:22:08.273399
#23 24.79 ERROR tests/test_order_processing.py::TestOrderProcessing::test_cancel_order
2025-May-26 09:22:08.273399
#23 24.79 ERROR tests/test_order_processing.py::TestOrderProcessing::test_list_user_orders
2025-May-26 09:22:08.273399
#23 24.79 ERROR tests/test_payment_processing.py::TestPaymentProcessing::test_initiate_payment
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_payment_processing.py::TestPaymentProcessing::test_initiate_payment_already_paid
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_payment_processing.py::TestPaymentProcessing::test_payment_callback_success
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_payment_processing.py::TestPaymentProcessing::test_payment_callback_failure
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_payment_processing.py::TestPaymentProcessing::test_webhook_processing
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_phonepe_service.py::TestPhonePeService::test_initiate_payment
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_phonepe_service.py::TestPhonePeService::test_initiate_payment_error
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_stability_robustness.py::TestStabilityRobustness::test_phonepe_service_initialization_failure
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_stability_robustness.py::TestStabilityRobustness::test_phonepe_payment_initiation_failure
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_stability_robustness.py::TestStabilityRobustness::test_invalid_order_id_in_callback
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_stability_robustness.py::TestStabilityRobustness::test_missing_order_id_in_callback
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_stability_robustness.py::TestStabilityRobustness::test_invalid_signature_in_webhook
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_stability_robustness.py::TestStabilityRobustness::test_missing_signature_in_webhook
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_stability_robustness.py::TestStabilityRobustness::test_invalid_transaction_id_in_webhook
2025-May-26 09:22:08.273399
#23 24.80 ERROR tests/test_stability_robustness.py::TestStabilityRobustness::test_concurrent_payment_processing
2025-May-26 09:22:08.273399
#23 24.80 ======================== 14 passed, 25 errors in 21.86s ========================